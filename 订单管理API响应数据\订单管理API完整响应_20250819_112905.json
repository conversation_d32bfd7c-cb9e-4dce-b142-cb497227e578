{"request_time": "2025-08-19 11:29:05", "api_url": "https://agentseller.temu.com/mms/venom/api/supplier/purchase/manager/querySubOrderList", "request_method": "POST", "request_data": {"pageNo": 1, "pageSize": 100, "urgencyType": 1, "isCustomGoods": false, "statusList": [1], "oneDimensionSort": {"firstOrderByParam": "expectLatestDeliverTime", "firstOrderByDesc": 0}}, "response_status": 200, "response_status_text": "", "response_headers": {}, "response_body": {"success": true, "errorCode": 1000000, "errorMsg": null, "result": {"subOrderForSupplierList": [], "total": 0, "statusNumMap": {"0": 0, "1": 0, "2": 0, "3": 0, "5": 0, "6": 0, "7": 0, "8": 0, "9": 0, "10": 0}, "delayNumMap": {"101": 0, "102": 0, "201": 0, "202": 0}, "todayCanDeliverNum": 0, "todayPlatformPurchaseNum": 0, "closeJitPurchaseNum": 0, "inFulfilmentPunishNum": 0, "currentTime": 1755574145413, "delayStatisticsConfig": {"deliverUpcomingDelayTimeMillis": 21600000, "arrivalUpcomingDelayTimeMillis": 21600000, "deliverDisplayCountdownMillis": 3600000, "arrivalDisplayCountdownMillis": 3600000}, "isMallClose": false, "hotInventoryLackNum": 0}}, "description": "订单管理API的完整响应信息"}