#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DrissionPage实践例子：监听订单管理API并发起自定义请求
功能：打开指定页面，监听API请求，获取认证信息，然后发起自定义请求
目标页面：https://agentseller.temu.com/stock/fully-mgt/order-manage-urgency
目标API：https://agentseller.temu.com/mms/venom/api/supplier/purchase/manager/querySubOrderList
"""

from DrissionPage import Chromium
import json
import time
import os
from datetime import datetime

def create_save_directory():
    """
    创建保存响应数据的目录
    """
    save_dir = "订单管理API响应数据"
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
        print(f"📁 创建保存目录：{save_dir}")
    return save_dir

def save_response_to_file(data, filename_prefix="订单API响应"):
    """
    将响应数据保存到JSON文件
    """
    save_dir = create_save_directory()
    
    # 生成带时间戳的文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"{filename_prefix}_{timestamp}.json"
    filepath = os.path.join(save_dir, filename)
    
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=4, ensure_ascii=False)
        print(f"💾 响应数据已保存到文件：{filepath}")
        return filepath
    except Exception as e:
        print(f"❌ 保存文件失败：{e}")
        return None

def open_target_page_and_monitor():
    """
    打开目标页面并启动API监听
    """
    print("🚀 开始打开目标页面并启动API监听...")
    
    # 连接到9222端口的浏览器
    browser = Chromium(9222)
    print("✅ 已连接到浏览器（端口9222）")
    
    # 新建标签页
    new_tab = browser.new_tab()
    print("📄 新建空白标签页完成")
    
    # 激活标签页（显示在前台）
    new_tab.set.activate()
    print("🎯 标签页已激活")
    
    # 启动网络监听器 - 监听目标API
    target_api = "mms/venom/api/supplier/purchase/manager/querySubOrderList"
    print("🔍 启动网络监听器...")
    print(f"🎯 目标API: {target_api}")
    new_tab.listen.start(targets=target_api)
    print("✅ 网络监听器已启动，开始监控订单管理API")
    
    # 访问目标页面
    target_url = "https://agentseller.temu.com/stock/fully-mgt/order-manage-urgency"
    print(f"🌐 正在访问目标页面：{target_url}")
    
    # 记录开始时间
    start_time = time.time()
    
    # 访问页面
    new_tab.get(target_url)

    # 立即尝试捕获API请求（在页面加载过程中）
    print("⚡ 立即检查页面加载过程中的API请求...")
    try:
        # 短时间等待，捕获页面加载时的API请求
        packet = new_tab.listen.wait(timeout=3)
        if packet:
            load_time = time.time() - start_time
            print(f"🎉 在页面加载过程中立即捕获到API请求！")
            print(f"⚡ 快速捕获时间：{load_time:.2f}秒")
            return new_tab, packet
    except:
        pass

    print("⏳ 等待页面开始加载...")
    new_tab.wait.load_start()
    print("✅ 页面开始加载")

    # 再次尝试捕获API请求
    print("⚡ 页面开始加载后立即检查API请求...")
    try:
        packet = new_tab.listen.wait(timeout=2)
        if packet:
            load_time = time.time() - start_time
            print(f"🎉 在页面加载开始后立即捕获到API请求！")
            print(f"⚡ 快速捕获时间：{load_time:.2f}秒")
            return new_tab, packet
    except:
        pass

    print("⏳ 等待文档加载完成...")
    new_tab.wait.doc_loaded()
    print("✅ 文档加载完成")

    # 计算加载时间
    load_time = time.time() - start_time
    print(f"⏱️ 页面完整加载时间：{load_time:.2f}秒")

    return new_tab, None

def capture_api_request(tab):
    """
    捕获目标API请求并获取完整的认证信息
    """
    print("\n" + "=" * 80)
    print("📡 开始监听目标API请求...")
    print("=" * 80)
    
    print("⏳ 快速等待目标API请求...")
    print("💡 提示：优先捕获页面加载过程中的API请求")

    try:
        # 先尝试短时间快速捕获
        print("⚡ 第一轮快速捕获（3秒）...")
        packet = tab.listen.wait(timeout=3)
        if packet:
            print("🎉 快速捕获成功！")
            return packet

        print("⚡ 第二轮捕获（5秒）...")
        packet = tab.listen.wait(timeout=5)
        if packet:
            print("✅ 捕获成功！")
            return packet

        print("⚡ 最后一轮捕获（10秒）...")
        packet = tab.listen.wait(timeout=10)
        if packet:
            print("✅ 最终捕获成功！")
            return packet

        print("⚠️ 未捕获到目标API请求")
        print("💡 可能的原因：")
        print("   1. API请求已经在监听启动前完成")
        print("   2. 页面没有发起该API请求")
        print("   3. API路径发生了变化")
        print("   4. 页面需要登录或其他操作才会触发API")
        return None
    except Exception as e:
        print(f"❌ 等待API请求时发生错误：{e}")
        return None

def display_captured_api_info(packet):
    """
    显示捕获到的API请求信息
    """
    print("✅ 成功捕获到目标API请求！")

    # 显示捕获到的API请求信息
    print("\n" + "=" * 80)
    print("🎯 捕获到的API请求信息")
    print("=" * 80)

    print(f"🔗 请求URL: {packet.url}")
    print(f"📋 请求方法: {packet.method}")
    print(f"📂 资源类型: {packet.resourceType}")

    # 显示请求头信息
    if packet.request and packet.request.headers:
        print(f"\n📋 请求头信息 (共{len(packet.request.headers)}个):")
        for key, value in packet.request.headers.items():
            # 只显示重要的认证相关头信息，避免输出过长
            if key.lower() in ['cookie', 'authorization', 'anti-content', 'mallid', 'user-agent', 'referer']:
                print(f"   {key}: {value[:100]}..." if len(str(value)) > 100 else f"   {key}: {value}")

    # 显示POST数据
    if packet.request and packet.request.postData:
        print(f"\n📝 POST数据:")
        try:
            if isinstance(packet.request.postData, dict):
                formatted_data = json.dumps(packet.request.postData, indent=4, ensure_ascii=False)
                print(formatted_data)
            else:
                try:
                    parsed_data = json.loads(str(packet.request.postData))
                    formatted_data = json.dumps(parsed_data, indent=4, ensure_ascii=False)
                    print(formatted_data)
                except:
                    print(f"   {packet.request.postData}")
        except Exception as e:
            print(f"   解析POST数据出错: {e}")

    return packet

def extract_auth_headers(packet):
    """
    从捕获的请求中提取认证头信息
    """
    print("\n🔍 提取认证头信息...")

    if not packet or not packet.request:
        print("❌ 无法获取请求信息")
        return None

    # 提取重要的认证头，过滤掉可能有问题的头
    auth_headers = {}
    # 定义需要的重要认证头
    important_headers = [
        'Anti-Content', 'mallid', 'cookie', 'authorization',
        'x-csrf-token', 'x-requested-with', 'user-agent',
        'accept', 'accept-language', 'content-type',
        'origin', 'referer'
    ]

    if packet.request.headers:
        for key, value in packet.request.headers.items():
            # 只保留重要的认证头，并且确保头名称是有效的
            if any(important_header.lower() == key.lower() for important_header in important_headers):
                # 过滤掉可能有问题的字符
                clean_key = key.strip()
                clean_value = str(value).strip()
                if clean_key and clean_value:
                    auth_headers[clean_key] = clean_value

    print(f"✅ 成功提取了 {len(auth_headers)} 个重要认证头")

    # 保存认证信息到文件
    auth_data = {
        'extraction_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        'original_url': packet.url,
        'method': packet.method,
        'headers': auth_headers,
        'original_post_data': packet.request.postData if packet.request.postData else None,
        'description': '从真实API请求中提取的重要认证信息'
    }

    save_response_to_file(auth_data, "提取的认证信息")

    return auth_headers

def send_custom_api_request(tab, auth_headers):
    """
    使用提取的认证信息发起自定义API请求
    """
    print("\n" + "=" * 80)
    print("📡 使用认证信息发起自定义API请求")
    print("=" * 80)
    
    if not auth_headers:
        print("❌ 没有认证头信息，无法发起请求")
        return None
    
    # 自定义的请求数据（根据用户要求）
    custom_request_data = {
        "pageNo": 1,
        "pageSize": 100,
        "urgencyType": 1,
        "isCustomGoods": False,
        "statusList": [1],
        "oneDimensionSort": {
            "firstOrderByParam": "expectLatestDeliverTime",
            "firstOrderByDesc": 0
        }
    }
    
    # API地址
    api_url = "https://agentseller.temu.com/mms/venom/api/supplier/purchase/manager/querySubOrderList"
    
    print(f"🎯 API地址：{api_url}")
    print(f"📋 请求方法：POST")
    print(f"📝 自定义请求数据：")
    print(json.dumps(custom_request_data, indent=4, ensure_ascii=False))
    
    try:
        print("\n⏳ 正在发起自定义API请求...")

        # 先检查页面是否还活跃
        try:
            current_url = tab.url
            print(f"📍 当前页面URL：{current_url}")
        except Exception as e:
            print(f"⚠️ 页面连接可能有问题：{e}")
            print("🔄 尝试重新连接...")

        # 使用更简单的JavaScript代码发起请求
        js_code = f'''
        (async function() {{
            try {{
                console.log('开始发起API请求...');
                const response = await fetch('{api_url}', {{
                    method: 'POST',
                    headers: {json.dumps(auth_headers)},
                    body: JSON.stringify({json.dumps(custom_request_data)})
                }});

                console.log('API请求已发送，状态码:', response.status);
                const responseText = await response.text();
                console.log('响应内容长度:', responseText.length);

                const result = {{
                    status: response.status,
                    statusText: response.statusText,
                    body: responseText,
                    success: true
                }};

                window.customOrderApiResponse = result;
                return result;
            }} catch (error) {{
                console.error('API请求失败:', error);
                const errorResult = {{
                    error: error.message,
                    success: false
                }};
                window.customOrderApiResponse = errorResult;
                return errorResult;
            }}
        }})();
        '''

        # 清除之前的响应
        tab.run_js('window.customOrderApiResponse = null;')

        # 执行JavaScript代码
        print("🔄 执行JavaScript请求代码...")
        tab.run_js(js_code)

        # 等待请求完成，使用快速轮询方式检查
        print("⏳ 快速等待API请求完成...")
        max_wait_time = 10  # 最多等待10秒
        wait_interval = 0.5   # 每0.5秒检查一次，提高响应速度

        for i in range(int(max_wait_time / wait_interval)):
            time.sleep(wait_interval)
            try:
                response_data = tab.run_js('return window.customOrderApiResponse;')
                if response_data is not None:
                    actual_time = (i + 1) * wait_interval
                    print(f"⚡ 在{actual_time:.1f}秒快速获取到响应数据")
                    break
                else:
                    if (i + 1) % 2 == 0:  # 每秒显示一次状态
                        actual_time = (i + 1) * wait_interval
                        print(f"⏳ {actual_time:.1f}秒：仍在等待响应...")
            except Exception as e:
                actual_time = (i + 1) * wait_interval
                print(f"⚠️ {actual_time:.1f}秒检查时出错：{e}")
        else:
            print("⏰ 等待超时，未获取到响应")
            return None

        if response_data:
            print("✅ 自定义API请求处理完成！")

            # 检查是否有错误
            if not response_data.get('success', True) or 'error' in response_data:
                error_msg = response_data.get('error', '未知错误')
                print(f"❌ 请求出错：{error_msg}")
                return None
            
            print(f"📥 响应状态码：{response_data.get('status', 'Unknown')}")
            print(f"📄 响应状态文本：{response_data.get('statusText', 'Unknown')}")
            
            # 显示响应内容
            print("\n📄 API响应内容：")
            print("=" * 70)
            
            try:
                # 尝试解析JSON响应
                response_json = json.loads(response_data['body'])
                formatted_response = json.dumps(response_json, indent=4, ensure_ascii=False)
                print(formatted_response)
                
                # 构建完整的响应数据并保存
                complete_response_data = {
                    'request_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'api_url': api_url,
                    'request_method': 'POST',
                    'request_data': custom_request_data,
                    'response_status': response_data.get('status'),
                    'response_status_text': response_data.get('statusText'),
                    'response_headers': response_data.get('headers', {}),
                    'response_body': response_json,
                    'description': '订单管理API的完整响应信息'
                }
                
                save_response_to_file(complete_response_data, "订单管理API完整响应")
                
            except Exception as e:
                print(f"⚠️ JSON解析失败：{e}")
                print("原始响应内容：")
                print(response_data['body'])
                
                # 保存原始响应
                raw_response_data = {
                    'request_time': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    'api_url': api_url,
                    'request_data': custom_request_data,
                    'response_status': response_data.get('status'),
                    'response_body_raw': response_data['body'],
                    'json_parse_error': str(e),
                    'description': '订单管理API原始响应（JSON解析失败）'
                }
                
                save_response_to_file(raw_response_data, "订单管理API原始响应")
            
            print("=" * 70)
            return response_data
        else:
            print("❌ 未获取到响应数据")
            return None
            
    except Exception as e:
        print(f"❌ API请求失败：{e}")
        return None

if __name__ == "__main__":
    """
    主程序：执行完整的订单管理API监听和请求流程
    """
    print("=" * 80)
    print("📚 DrissionPage实践例子：订单管理API监听和请求")
    print("🎯 目标：监听订单管理API，获取认证信息，发起自定义请求")
    print("=" * 80)
    
    try:
        # 步骤1：打开目标页面并启动监听
        print("🚀 第一步：打开目标页面并启动API监听")
        result = open_target_page_and_monitor()

        if result[0]:  # tab存在
            tab = result[0]
            early_packet = result[1]  # 可能在页面加载时就捕获到的包

            print(f"✅ 页面打开成功")
            print(f"📄 当前页面标题：{tab.title}")
            print(f"🔗 当前页面URL：{tab.url}")

            # 检查是否已经在页面加载过程中捕获到API请求
            if early_packet:
                print("\n🎉 已在页面加载过程中快速捕获到API请求！")
                print("⚡ 跳过额外等待，直接处理API请求")
                packet = early_packet
                # 显示捕获到的API信息
                display_captured_api_info(packet)
            else:
                # 步骤2：继续捕获API请求
                print("\n🚀 第二步：继续捕获目标API请求")
                packet = capture_api_request(tab)
                if packet:
                    # 显示捕获到的API信息
                    display_captured_api_info(packet)

            if packet:
                # 步骤3：提取认证信息
                print("\n🚀 第三步：提取认证信息")
                auth_headers = extract_auth_headers(packet)
                
                if auth_headers:
                    # 步骤4：发起自定义API请求
                    print("\n🚀 第四步：发起自定义API请求")
                    custom_response = send_custom_api_request(tab, auth_headers)
                    
                    if custom_response and custom_response.get('status') == 200:
                        print("\n🎉 订单管理API请求成功完成！")
                        print("📋 操作总结：")
                        print("   ✅ 成功打开目标页面")
                        print("   ✅ 成功监听到目标API请求")
                        print("   ✅ 成功提取认证信息")
                        print("   ✅ 成功发起自定义API请求")
                        print("   ✅ 获取到完整的响应数据")
                        print("   💾 所有数据已保存到JSON文件")
                    else:
                        print("\n⚠️ 自定义API请求未成功")
                        print("💡 可能需要检查认证信息或请求参数")
                else:
                    print("\n❌ 无法提取认证信息")
            else:
                print("\n❌ 未能捕获到目标API请求")
                print("💡 建议：")
                print("   1. 确保页面会自动发起API请求")
                print("   2. 尝试手动刷新页面")
                print("   3. 检查是否需要登录")
                print("   4. 确认API路径是否正确")
            
            # 停止监听器
            tab.listen.stop()
            print("\n✅ 网络监听器已停止")
        else:
            print("❌ 页面打开失败")
        
    except Exception as e:
        print(f"❌ 程序执行过程中发生错误：{e}")
        print("💡 请检查：")
        print("   1. 浏览器是否正确启动（端口9222）")
        print("   2. 网络连接是否正常")
        print("   3. 目标网址是否可访问")
        print("   4. 是否需要登录认证")
    
    print("\n" + "=" * 80)
    print("📚 订单管理API监听和请求实践完成")
    print("💡 这个例子展示了如何监听真实API请求并复制认证信息")
    print("📁 所有响应数据保存在：订单管理API响应数据/ 目录")
    print("🔒 注意：为了安全，标签页和浏览器保持打开状态")
    print("=" * 80)
